import { useRouter, useLocalSearchParams } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import SquadPage from '@/pages/Teams/SquadPage';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentById } from '@/hooks/useTournamentById';
import { useState, useCallback } from 'react';
import { useFocusEffect } from '@react-navigation/native';
import { fetchTeamById } from '@/services/teamsService';
import { toast } from '@/toast/toast';
import { type Team } from '@/types/teams';

export default function TeamSquadScreen() {
  const router = useRouter();
  const {
    'team-id': teamId,
    'tournament-id': tournamentId,
    mode = 'create',
  } = useLocalSearchParams();

  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const { tournament } = useTournamentById(tournamentId as string);

  const loadTeam = useCallback(async () => {
    if (!teamId) return;

    setLoading(true);
    try {
      const {
        success,
        team: fetchedTeam,
        error,
      } = await fetchTeamById(teamId as string);

      if (!success) {
        toast.error(error || 'Failed to load team details');
        router.back();
        return;
      }

      setTeam(fetchedTeam);
    } catch (error) {
      toast.error('Something went wrong');
      router.back();
    } finally {
      setLoading(false);
    }
  }, [teamId, router]);

  useFocusEffect(
    useCallback(() => {
      loadTeam();
    }, [loadTeam])
  );

  const getTitle = () => {
    if (mode === 'edit') {
      return `Edit ${team?.name || 'Team'} Squad`;
    }
    return `Build ${team?.name || 'Team'} Squad`;
  };

  return (
    <NavLayout title={getTitle()} isFullscreen noScroll>
      {loading ? (
        <FullscreenLoader />
      ) : team && tournament ? (
        <SquadPage
          team={team}
          tournament={tournament}
          mode={mode as 'create' | 'edit'}
        />
      ) : null}
    </NavLayout>
  );
}
