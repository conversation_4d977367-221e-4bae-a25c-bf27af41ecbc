import { useRouter, useLocalSearchParams } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import TeamForm from '@/pages/Teams/TeamForm';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentById } from '@/hooks/useTournamentById';
import { type TeamFormData } from '@/types/teams';
import { createTeam } from '@/services/teamsService';
import { toast } from '@/toast/toast';

export default function CreateTeamModal() {
  const router = useRouter();
  const { 'tournament-id': tournamentId } = useLocalSearchParams();
  const { tournament, loading } = useTournamentById(tournamentId as string);

  const handleSubmit = async (formData: TeamFormData) => {
    const { success, error, team } = await createTeam({
      tournamentId: tournamentId as string,
      team: formData,
    });

    if (!success) {
      toast.error(error || 'Failed to create team. Please try again.');
      return;
    }

    router.push({
      pathname: '/screens/teams/[team-id]/squad',
      params: {
        'team-id': team.id,
        'tournament-id': tournamentId,
        mode: 'create',
      },
    });
  };

  return (
    <NavLayout title="Create Team">
      {loading ? (
        <FullscreenLoader />
      ) : tournament ? (
        <TeamForm
          onSubmit={handleSubmit}
          submitButtonText="Create Team"
          isLoading={false}
          tournamentId={tournamentId as string}
        />
      ) : null}
    </NavLayout>
  );
}
