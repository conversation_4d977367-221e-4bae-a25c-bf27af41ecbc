import { useState, useCallback } from 'react';
import { useFocusEffect } from 'expo-router';
import { fetchPlayers, updatePlayer } from '@/services/playerService';
import { toast } from '@/toast/toast';
import { type Player } from '@/types/player';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';

interface UseSquadManagementProps {
  teamId: string;
  tournamentId: string;
}

interface UseSquadManagementReturn {
  players: Player[];
  loading: boolean;
  totalCount: number;
  availablePlayers: Option[];
  hasMorePlayers: boolean;
  loadingPlayers: boolean;
  playerOperations: Set<string>;
  loadPlayers: () => Promise<void>;
  loadAvailablePlayers: (
    searchQuery: string,
    isNewSearch?: boolean
  ) => Promise<void>;
  loadMorePlayers: () => Promise<void>;
  handlePlayerSelect: (selected: Option | Option[] | null) => Promise<void>;
  handleRemovePlayer: (playerId: string) => Promise<void>;
  handleSearchChange: (query: string) => void;
  resetAvailablePlayers: () => void;
}

export function useSquadManagement({
  teamId,
  tournamentId,
}: UseSquadManagementProps): UseSquadManagementReturn {
  // Squad players state
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  // Available players state
  const [availablePlayers, setAvailablePlayers] = useState<Option[]>([]);
  const [hasMorePlayers, setHasMorePlayers] = useState(false);
  const [loadingPlayers, setLoadingPlayers] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [currentSearchQuery, setCurrentSearchQuery] = useState('');

  const [playerOperations, setPlayerOperations] = useState<Set<string>>(
    new Set()
  );

  const loadPlayers = useCallback(async () => {
    setLoading(true);
    try {
      const {
        players: fetchedPlayers,
        count,
        error,
      } = await fetchPlayers({
        tournamentId,
        teamId,
        limit: 50,
      });

      if (error) {
        toast.error('Failed to load squad players');
        return;
      }

      setPlayers(fetchedPlayers || []);
      setTotalCount(count || 0);
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  }, [teamId, tournamentId]);

  // Load squad players on focus
  useFocusEffect(
    useCallback(() => {
      loadPlayers();
    }, [loadPlayers])
  );

  // Load available players for selection (exclude players already on teams)
  const loadAvailablePlayers = useCallback(
    async (searchQuery: string, isNewSearch = true) => {
      setLoadingPlayers(true);

      const pageToLoad = isNewSearch ? 1 : currentPage + 1;

      try {
        const {
          players: fetchedPlayers,
          count,
          error,
        } = await fetchPlayers({
          tournamentId,
          search: searchQuery,
          page: pageToLoad,
          limit: 20,
        });

        if (error) {
          toast.error('Failed to load available players');
          return;
        }

        // Filter out players who already have a team_id
        const unassignedPlayers = (fetchedPlayers || []).filter(
          (player: Player) => !player.team_id
        );

        const playerOptions: Option[] = unassignedPlayers.map(
          (player: Player) => ({
            label: player.name,
            value: player.id,
            player,
          })
        );

        if (isNewSearch) {
          setAvailablePlayers(playerOptions);
          setCurrentPage(1);
          setCurrentSearchQuery(searchQuery);
        } else {
          setAvailablePlayers((prev) => [...prev, ...playerOptions]);
          setCurrentPage(pageToLoad);
        }

        setHasMorePlayers((count || 0) > pageToLoad * 20);
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setLoadingPlayers(false);
      }
    },
    [tournamentId, currentPage]
  );

  // Load more players for pagination
  const loadMorePlayers = useCallback(async () => {
    if (!loadingPlayers && hasMorePlayers) {
      await loadAvailablePlayers(currentSearchQuery, false);
    }
  }, [
    loadAvailablePlayers,
    currentSearchQuery,
    loadingPlayers,
    hasMorePlayers,
  ]);

  const handleSearchChange = useCallback(
    (query: string) => {
      loadAvailablePlayers(query, true);
    },
    [loadAvailablePlayers]
  );

  const handlePlayerSelect = useCallback(
    async (selected: Option | Option[] | null) => {
      if (!selected) return;

      const selectedPlayers = Array.isArray(selected) ? selected : [selected];
      if (selectedPlayers.length === 0) return;

      const playerIds = selectedPlayers.map((p) => p.value);
      setPlayerOperations((prev) => {
        const newSet = new Set(prev);
        playerIds.forEach((id) => newSet.add(id));
        return newSet;
      });

      try {
        const updatePromises = selectedPlayers.map(async (playerOption) => {
          const { success, error } = await updatePlayer(playerOption.value, {
            team_id: teamId,
          });

          if (!success) {
            throw new Error(
              error || `Failed to add ${playerOption.label} to squad`
            );
          }

          return playerOption.label;
        });

        const results = await Promise.allSettled(updatePromises);
        await loadPlayers(); // Refresh the list

        const successNames: string[] = [];
        const failedMessages: string[] = [];

        results.forEach((result) => {
          if (result.status === 'fulfilled') {
            successNames.push(result.value);
          } else {
            failedMessages.push(result.reason?.message || 'Unknown error');
          }
        });

        if (successNames.length > 0) {
          toast.success(
            successNames.length === 1
              ? `${successNames[0]} added to squad successfully`
              : `${successNames.length} players added to squad successfully`
          );
        }

        if (failedMessages.length > 0) {
          toast.error(
            failedMessages.length === 1
              ? failedMessages[0]
              : `Failed to add ${failedMessages.length} players to squad`
          );
        }
      } catch (error) {
        toast.error(
          error instanceof Error
            ? error.message
            : 'Something went wrong while adding players'
        );
      } finally {
        setPlayerOperations((prev) => {
          const newSet = new Set(prev);
          playerIds.forEach((id) => newSet.delete(id));
          return newSet;
        });
      }
    },
    [teamId, loadPlayers]
  );

  const handleRemovePlayer = useCallback(
    async (playerId: string) => {
      setPlayerOperations((prev) => new Set(prev).add(playerId));

      try {
        const { success, error } = await updatePlayer(playerId, {
          team_id: null,
        });

        if (!success) {
          toast.error(error || 'Failed to remove player from squad');
          return;
        }

        await loadPlayers();
        toast.success('Player removed from squad');
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setPlayerOperations((prev) => {
          const newSet = new Set(prev);
          newSet.delete(playerId);
          return newSet;
        });
      }
    },
    [loadPlayers]
  );

  const resetAvailablePlayers = useCallback(() => {
    setAvailablePlayers([]);
    setHasMorePlayers(false);
    setCurrentPage(1);
    setCurrentSearchQuery('');
  }, []);

  return {
    players,
    loading,
    totalCount,
    availablePlayers,
    hasMorePlayers,
    loadingPlayers,
    playerOperations,
    loadPlayers,
    loadAvailablePlayers,
    loadMorePlayers,
    handlePlayerSelect,
    handleRemovePlayer,
    handleSearchChange,
    resetAvailablePlayers,
  };
}
