import { useState, useCallback } from 'react';
import { useFocusEffect } from 'expo-router';
import { fetchPlayers, updatePlayer } from '@/services/playerService';
import { toast } from '@/toast/toast';
import { type Player } from '@/types/player';
import { type Option } from '@/components/k-components/AsyncSelectWithSearch';

interface UseSquadManagementProps {
  teamId: string;
  tournamentId: string;
}

interface UseSquadManagementReturn {
  players: Player[];
  loading: boolean;
  totalCount: number;
  availablePlayers: Option[];
  hasMorePlayers: boolean;
  loadingPlayers: boolean;
  playerOperations: Set<string>;
  loadPlayers: () => Promise<void>;
  loadAvailablePlayers: (
    searchQuery: string,
    isNewSearch?: boolean
  ) => Promise<void>;
  loadMorePlayers: (searchQuery: string, page: number) => Promise<void>;
  handlePlayerSelect: (selected: Option | Option[] | null) => Promise<void>;
  handleRemovePlayer: (playerId: string) => Promise<void>;
  handleSearchChange: (query: string) => void;
  resetAvailablePlayers: () => void;
  handlePlayerAdded: (newPlayer: Player) => void;
}

export function useSquadManagement({
  teamId,
  tournamentId,
}: UseSquadManagementProps): UseSquadManagementReturn {
  // Squad players state
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  // Available players state
  const [availablePlayers, setAvailablePlayers] = useState<Option[]>([]);
  const [hasMorePlayers, setHasMorePlayers] = useState(false);
  const [loadingPlayers, setLoadingPlayers] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [consecutiveEmptyPages, setConsecutiveEmptyPages] = useState(0);

  const [playerOperations, setPlayerOperations] = useState<Set<string>>(
    new Set()
  );

  const loadPlayers = useCallback(async () => {
    setLoading(true);
    try {
      const {
        players: fetchedPlayers,
        count,
        error,
      } = await fetchPlayers({
        tournamentId,
        teamId,
        limit: 50,
      });

      if (error) {
        toast.error('Failed to load squad players');
        return;
      }

      setPlayers(fetchedPlayers || []);
      setTotalCount(count || 0);
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  }, [teamId, tournamentId]);

  // Load squad players on focus
  useFocusEffect(
    useCallback(() => {
      loadPlayers();
    }, [loadPlayers])
  );

  // Load available players for selection (exclude players already on teams)
  const loadAvailablePlayers = useCallback(
    async (searchQuery: string, isNewSearch = true, pageToLoad?: number) => {
      setLoadingPlayers(true);

      const targetPage = pageToLoad || (isNewSearch ? 1 : currentPage + 1);

      try {
        const { players: fetchedPlayers, error } = await fetchPlayers({
          tournamentId,
          search: searchQuery,
          page: targetPage,
          limit: 20,
        });

        if (error) {
          toast.error('Failed to load available players');
          return;
        }

        // Filter out players who already have a team_id
        const unassignedPlayers = (fetchedPlayers || []).filter(
          (player: Player) => !player.team_id
        );

        const playerOptions: Option[] = unassignedPlayers.map(
          (player: Player) => ({
            label: player.name,
            value: player.id,
            player,
          })
        );

        if (isNewSearch) {
          setAvailablePlayers(playerOptions);
          setCurrentPage(1);
          setConsecutiveEmptyPages(0);
        } else {
          setAvailablePlayers((prev) => [...prev, ...playerOptions]);
          setCurrentPage(targetPage);
        }

        // Track consecutive pages with no unassigned players
        const hasUnassignedPlayers = unassignedPlayers.length > 0;
        if (!isNewSearch) {
          if (hasUnassignedPlayers) {
            setConsecutiveEmptyPages(0);
          } else {
            setConsecutiveEmptyPages((prev) => prev + 1);
          }
        }

        // Determine if there are more pages to load
        const receivedFullPage = (fetchedPlayers || []).length === 20;
        const tooManyEmptyPages = consecutiveEmptyPages >= 3; // Stop after 3 consecutive empty pages
        const shouldContinue = receivedFullPage && !tooManyEmptyPages;
        setHasMorePlayers(shouldContinue);

        // Debug logging
        console.log('Pagination Debug:', {
          targetPage,
          fetchedPlayersCount: (fetchedPlayers || []).length,
          unassignedPlayersCount: unassignedPlayers.length,
          receivedFullPage,
          consecutiveEmptyPages,
          tooManyEmptyPages,
          hasMoreData: shouldContinue,
        });
      } catch (error) {
        toast.error('Something went wrong');
      } finally {
        setLoadingPlayers(false);
      }
    },
    [tournamentId, currentPage]
  );

  // Load more players for pagination
  const loadMorePlayers = useCallback(
    async (searchQuery: string, page: number) => {
      if (!loadingPlayers && hasMorePlayers) {
        await loadAvailablePlayers(searchQuery, false, page);
      }
    },
    [loadAvailablePlayers, loadingPlayers, hasMorePlayers]
  );

  const handleSearchChange = useCallback(
    (query: string) => {
      loadAvailablePlayers(query, true);
    },
    [loadAvailablePlayers]
  );

  const handlePlayerSelect = useCallback(
    async (selected: Option | Option[] | null) => {
      if (!selected) return;

      const selectedPlayers = Array.isArray(selected) ? selected : [selected];
      if (selectedPlayers.length === 0) return;

      const playerIds = selectedPlayers.map((p) => p.value);
      setPlayerOperations((prev) => {
        const newSet = new Set(prev);
        playerIds.forEach((id) => newSet.add(id));
        return newSet;
      });

      try {
        const updatePromises = selectedPlayers.map(async (playerOption) => {
          const { success, error } = await updatePlayer(playerOption.value, {
            team_id: teamId,
          });

          if (!success) {
            throw new Error(
              error || `Failed to add ${playerOption.label} to squad`
            );
          }

          return playerOption.label;
        });

        const results = await Promise.allSettled(updatePromises);

        const successfulPlayers: Option[] = [];
        const successNames: string[] = [];
        const failedMessages: string[] = [];

        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            successfulPlayers.push(selectedPlayers[index]);
            successNames.push(result.value);
          } else {
            failedMessages.push(result.reason?.message || 'Unknown error');
          }
        });

        // Update local state for successful additions
        if (successfulPlayers.length > 0) {
          setPlayers((prev) => [
            ...prev,
            ...successfulPlayers.map((option) => ({
              ...option.player,
              team_id: teamId,
            })),
          ]);
          setTotalCount((prev) => prev + successfulPlayers.length);

          // Remove successfully added players from available players list
          setAvailablePlayers((prev) =>
            prev.filter(
              (player) =>
                !successfulPlayers.some((sp) => sp.value === player.value)
            )
          );
        }

        if (successNames.length > 0) {
          toast.success(
            successNames.length === 1
              ? `${successNames[0]} added to squad successfully`
              : `${successNames.length} players added to squad successfully`
          );
        }

        if (failedMessages.length > 0) {
          toast.error(
            failedMessages.length === 1
              ? failedMessages[0]
              : `Failed to add ${failedMessages.length} players to squad`
          );
        }
      } catch (error) {
        toast.error(
          error instanceof Error
            ? error.message
            : 'Something went wrong while adding players'
        );
      } finally {
        setPlayerOperations((prev) => {
          const newSet = new Set(prev);
          playerIds.forEach((id) => newSet.delete(id));
          return newSet;
        });
      }
    },
    [teamId]
  );

  const handleRemovePlayer = useCallback(async (playerId: string) => {
    setPlayerOperations((prev) => new Set(prev).add(playerId));

    try {
      const { success, error } = await updatePlayer(playerId, {
        team_id: null,
      });

      if (!success) {
        toast.error(error || 'Failed to remove player from squad');
        return;
      }
      setPlayers((prev) => prev.filter((p) => p.id !== playerId));
      setTotalCount((prev) => prev - 1);
      toast.success('Player removed from squad');
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setPlayerOperations((prev) => {
        const newSet = new Set(prev);
        newSet.delete(playerId);
        return newSet;
      });
    }
  }, []);

  const resetAvailablePlayers = useCallback(() => {
    setAvailablePlayers([]);
    setHasMorePlayers(false);
    setCurrentPage(1);
    setConsecutiveEmptyPages(0);
  }, []);

  // Handle manual player addition (from AddPlayer component)
  const handlePlayerAdded = useCallback((newPlayer: Player) => {
    setPlayers((prev) => [...prev, newPlayer]);
    setTotalCount((prev) => prev + 1);
  }, []);

  return {
    players,
    loading,
    totalCount,
    availablePlayers,
    hasMorePlayers,
    loadingPlayers,
    playerOperations,
    loadPlayers,
    loadAvailablePlayers,
    loadMorePlayers,
    handlePlayerSelect,
    handleRemovePlayer,
    handleSearchChange,
    resetAvailablePlayers,
    handlePlayerAdded,
  };
}
