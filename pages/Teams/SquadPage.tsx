import React, { useState, useEffect } from 'react';
import { View, ScrollView, Pressable } from 'react-native';
import { Icon } from '@/components/ui/icon';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText, ButtonIcon } from '@/components/ui/button';
import { PlusIcon, XIcon, UserPlusIcon } from 'lucide-react-native';
import { type Team } from '@/types/teams';
import { type Tournament } from '@/pages/TournamentViewScreen/types';
import { type Player } from '@/types/player';
import { useRouter } from 'expo-router';
import NoDataFound from '@/components/k-components/NoDataFound';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { Spinner } from '@/components/ui/spinner';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
} from '@/components/ui/actionsheet';
import { ActionsheetDragIndicator } from '@/components/ui/select/select-actionsheet';
import AddPlayer from '../Players/AddPlayer';
import { AsyncSelectWithSearch } from '@/components/k-components/AsyncSelectWithSearch';
import { toast } from '@/toast/toast';
import { CTAButton } from '@/components/ui/primaryCTAbutton';
import SCREENS from '@/constants/Screens';
import LogoImage from '@/components/k-components/LogoImage';
import { useSquadManagement } from './hooks/useSquadManagement';

interface SquadPageProps {
  team: Team;
  tournament: Tournament;
  mode: 'create' | 'edit';
}

const SquadPage: React.FC<SquadPageProps> = ({ team, tournament, mode }) => {
  const router = useRouter();
  const [showAddPlayer, setShowAddPlayer] = useState(false);
  const [showSelectPlayer, setShowSelectPlayer] = useState(false);

  const {
    players,
    loading,
    totalCount,
    availablePlayers,
    hasMorePlayers,
    loadingPlayers,
    playerOperations,
    loadPlayers,
    loadAvailablePlayers,
    loadMorePlayers,
    handlePlayerSelect,
    handleRemovePlayer,
    handleSearchChange,
    resetAvailablePlayers,
  } = useSquadManagement({
    teamId: team.id,
    tournamentId: tournament.id,
  });

  // Load available players when select modal opens
  useEffect(() => {
    if (showSelectPlayer) {
      loadAvailablePlayers('', true);
    } else {
      resetAvailablePlayers();
    }
  }, [showSelectPlayer, loadAvailablePlayers, resetAvailablePlayers]);

  const handlePlayerAdded = () => {
    setShowAddPlayer(false);
    loadPlayers();
    toast.success('Player added to squad successfully');
  };

  const handlePlayerSelectAndClose = async (selected: any) => {
    await handlePlayerSelect(selected);
    setShowSelectPlayer(false);
  };

  const handleContinue = () => {
    router.push({
      pathname: SCREENS.TEAM_VIEW,
      params: {
        'team-id': team.id,
        'tournament-id': tournament.id,
      },
    });
  };

  // Custom player card component with remove functionality
  const SquadPlayerCard = ({ player }: { player: Player }) => {
    const isLoading = playerOperations.has(player.id);

    return (
      <View className="flex-row items-center justify-between py-3 px-2 bg-white rounded-lg border-b border-outline-100">
        <HStack className="items-center space-x-3 flex-1">
          <LogoImage
            width={40}
            height={40}
            borderRadius={100}
            fallbackText={player.name}
            fallBacktextClassName={'text-xl font-urbanistBold'}
          />
          <VStack className="flex-1 ml-3">
            <Text className="text-base font-urbanistMedium text-typography-800">
              {player.name}
            </Text>
            {player.jersey_number && (
              <Text className="text-xs text-typography-600 font-urbanist">
                Jersey #{player.jersey_number}
              </Text>
            )}
          </VStack>
        </HStack>

        {isLoading ? (
          <Spinner size="small" className="text-red-500" />
        ) : (
          <Pressable
            onPress={() => handleRemovePlayer(player.id)}
            disabled={isLoading}
            className="w-6 h-6 items-center justify-center rounded-full border border-red-500"
          >
            <Icon as={XIcon} size="sm" className="text-red-500" />
          </Pressable>
        )}
      </View>
    );
  };

  const renderSquadHeader = () => (
    <VStack className="px-4 py-4 space-y-3">
      {totalCount > 0 && (
        <HStack className="items-center justify-between">
          <Text className="text-sm font-urbanistMedium text-typography-700">
            {totalCount} {totalCount === 1 ? 'Player' : 'Players'}
          </Text>
          <HStack className="space-x-2 gap-3">
            <Button
              size="sm"
              variant="outline"
              onPress={() => setShowSelectPlayer(true)}
            >
              <ButtonIcon as={UserPlusIcon} size="sm" />
              <ButtonText className="font-urbanistSemiBold">
                Select Player
              </ButtonText>
            </Button>
            <Button
              size="sm"
              className="bg-primary-0"
              onPress={() => setShowAddPlayer(true)}
            >
              <ButtonIcon as={PlusIcon} size="sm" />
              <ButtonText className="font-urbanistSemiBold">Add New</ButtonText>
            </Button>
          </HStack>
        </HStack>
      )}
    </VStack>
  );

  const renderPlayersList = () => {
    if (loading) {
      return <FullscreenLoader />;
    }

    if (players.length === 0) {
      return (
        <View className="px-4">
          <NoDataFound
            title="No Players in Squad"
            subtitle={
              mode === 'create'
                ? 'Start building your team by adding your first player.'
                : 'Add players to build your squad.'
            }
            action={
              <VStack className="space-y-3 w-full gap-4">
                <Button
                  variant="outline"
                  onPress={() => setShowSelectPlayer(true)}
                >
                  <ButtonIcon as={UserPlusIcon} />
                  <ButtonText className="font-urbanistSemiBold">
                    Select Existing Player
                  </ButtonText>
                </Button>
                <CTAButton
                  title="Add New Player"
                  lefticon={PlusIcon}
                  onPress={() => setShowAddPlayer(true)}
                />
              </VStack>
            }
          />
        </View>
      );
    }

    return (
      <VStack className="px-4 space-y-3">
        {players.map((player: Player) => (
          <SquadPlayerCard key={player.id} player={player} />
        ))}
      </VStack>
    );
  };

  return (
    <View className="flex-1">
      {renderSquadHeader()}
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        {renderPlayersList()}
      </ScrollView>

      <View className="px-4 py-4 bg-white border-t border-outline-100">
        <CTAButton title="Continue" onPress={handleContinue} />
      </View>

      {/* Add New Player Modal */}
      <Actionsheet
        isOpen={showAddPlayer}
        onClose={() => setShowAddPlayer(false)}
      >
        <ActionsheetBackdrop />
        <ActionsheetContent className="max-h-[90%]">
          <ActionsheetDragIndicator />
          <AddPlayer
            tournament={tournament}
            teamId={team.id}
            onClose={handlePlayerAdded}
          />
        </ActionsheetContent>
      </Actionsheet>

      {/* Select Existing Player Modal */}
      <AsyncSelectWithSearch
        isOpen={showSelectPlayer}
        onClose={() => setShowSelectPlayer(false)}
        onSelect={handlePlayerSelectAndClose}
        options={availablePlayers}
        hasMore={hasMorePlayers}
        loadMore={loadMorePlayers}
        onSearchChange={handleSearchChange}
        noDataFoundText="No players found"
        placeholder="Search for players..."
        multiple={true}
        renderOption={(option, isSelected) => (
          <VStack className="flex-1 py-2">
            <Text
              className={`text-base ${
                isSelected ? 'font-urbanistBold' : 'font-urbanistMedium'
              }`}
            >
              {option.label}
            </Text>
            {option.player?.jersey_number && (
              <Text
                className={`text-xs text-typography-600 ${
                  isSelected ? 'font-urbanistMedium' : 'font-urbanist'
                }`}
              >
                Jersey #{option.player.jersey_number}
              </Text>
            )}
          </VStack>
        )}
      />

      {loadingPlayers && (
        <View className="absolute inset-0 bg-black/20 items-center justify-center">
          <Spinner size="large" />
        </View>
      )}
    </View>
  );
};

export default SquadPage;
