import { useState, useEffect } from 'react';
import { fetchTeams } from '@/services/teamsService';
import { toast } from '@/toast/toast';

export const useTournamentTeams = (tournamentId: string) => {
  const [teams, setTeams] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const loadTeams = async () => {
    setLoading(true);
    try {
      const {
        teams: fetchedTeams,
        count,
        error,
      } = await fetchTeams({ tournamentId });

      if (error) {
        toast.error(error || 'Something went wrong!');
        return;
      }

      setTeams(fetchedTeams || []);
      setTotalCount(count || 0);
    } catch (error) {
      toast.error('Something went wrong!');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTeams();
  }, [tournamentId]);

  return {
    teams,
    totalCount,
    loading,
    loadTeams,
  };
};
